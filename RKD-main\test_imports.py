#!/usr/bin/env python3
"""
Test script to check if all imports work correctly
"""

print("Testing imports...")

try:
    import torch
    print(f"✓ PyTorch {torch.__version__} imported successfully")
    print(f"✓ CUDA available: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"✓ GPU: {torch.cuda.get_device_name(0)}")
except Exception as e:
    print(f"✗ PyTorch import failed: {e}")

try:
    import numpy as np
    print(f"✓ NumPy {np.__version__} imported successfully")
except Exception as e:
    print(f"✗ NumPy import failed: {e}")

try:
    import pandas as pd
    print(f"✓ Pandas {pd.__version__} imported successfully")
except Exception as e:
    print(f"✗ Pandas import failed: {e}")

try:
    import hdbscan
    print(f"✓ HDBSCAN imported successfully")
except Exception as e:
    print(f"✗ HDBSCAN import failed: {e}")

try:
    from experiment.CustomConfig import CustomConfig
    print("✓ CustomConfig imported successfully")
    
    config = CustomConfig()
    print(f"✓ CustomConfig instantiated successfully")
    print(f"✓ Non-IID: {config.nonIID}")
    print(f"✓ Alpha Dirichlet: {config.alphaDirichlet}")
    print(f"✓ Number of scenarios: {len(config.scenarios)}")
    print(f"✓ Number of aggregators: {len(config.aggregators)}")
    
except Exception as e:
    print(f"✗ CustomConfig import/instantiation failed: {e}")
    import traceback
    traceback.print_exc()

print("Import test completed!")
