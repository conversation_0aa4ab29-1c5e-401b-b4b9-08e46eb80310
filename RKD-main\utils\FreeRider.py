import torch
from torch import nn
from typing import <PERSON><PERSON>
from utils.typings import FreeRiderAttack


class FreeRider:
    """
    Utility class for handling free-rider detection and simulation.
    Free-riders are clients that don't perform actual training but try to benefit from the federated learning process.
    """
    
    def __init__(self, device: torch.device, attack_type: FreeRiderAttack):
        """
        Initialize the FreeRider utility.
        
        Args:
            device: The device to run computations on
            attack_type: The type of free-rider attack to simulate
        """
        self.device = device
        self.attack_type = attack_type
        
    def free_grads(self, current_model: nn.Module, prev_model: nn.Module) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Simulate gradients for a free-riding client.
        Free-riders typically return models with minimal or no changes.
        
        Args:
            current_model: The current model state
            prev_model: The previous model state
            
        Returns:
            Tuple of (mean, std) of the gradient norms
        """
        grad_norms = []
        
        for (name, current_param), (_, prev_param) in zip(
            current_model.named_parameters(), prev_model.named_parameters()
        ):
            if current_param.requires_grad:
                # Calculate the difference between current and previous parameters
                # For free-riders, this should be minimal
                param_diff = current_param.data - prev_param.data
                
                if self.attack_type == FreeRiderAttack.BASIC:
                    # Basic free-rider: no change or minimal change
                    grad_norm = torch.norm(param_diff).item()
                elif self.attack_type == FreeRiderAttack.NOISY:
                    # Noisy free-rider: add small random noise to appear legitimate
                    noise = torch.randn_like(param_diff) * 0.001
                    noisy_diff = param_diff + noise
                    grad_norm = torch.norm(noisy_diff).item()
                elif self.attack_type == FreeRiderAttack.DELTA:
                    # Delta free-rider: use a small fixed change
                    delta = torch.ones_like(param_diff) * 0.0001
                    delta_diff = param_diff + delta
                    grad_norm = torch.norm(delta_diff).item()
                else:
                    grad_norm = torch.norm(param_diff).item()
                
                grad_norms.append(grad_norm)
        
        if grad_norms:
            grad_norms_tensor = torch.tensor(grad_norms, device=self.device)
            mean = torch.mean(grad_norms_tensor)
            std = torch.std(grad_norms_tensor)
        else:
            mean = torch.tensor(0.0, device=self.device)
            std = torch.tensor(0.0, device=self.device)
            
        return mean, std
    
    def normal_grads(self, model: nn.Module) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Calculate gradient statistics for a normal (non-free-riding) client.
        
        Args:
            model: The model to analyze
            
        Returns:
            Tuple of (mean, std) of the gradient norms
        """
        grad_norms = []
        
        for name, param in model.named_parameters():
            if param.requires_grad and param.grad is not None:
                grad_norm = torch.norm(param.grad).item()
                grad_norms.append(grad_norm)
        
        if grad_norms:
            grad_norms_tensor = torch.tensor(grad_norms, device=self.device)
            mean = torch.mean(grad_norms_tensor)
            std = torch.std(grad_norms_tensor)
        else:
            # If no gradients are available, use parameter norms as a proxy
            param_norms = []
            for name, param in model.named_parameters():
                if param.requires_grad:
                    param_norm = torch.norm(param.data).item()
                    param_norms.append(param_norm)
            
            if param_norms:
                param_norms_tensor = torch.tensor(param_norms, device=self.device)
                mean = torch.mean(param_norms_tensor)
                std = torch.std(param_norms_tensor)
            else:
                mean = torch.tensor(0.0, device=self.device)
                std = torch.tensor(0.0, device=self.device)
            
        return mean, std
