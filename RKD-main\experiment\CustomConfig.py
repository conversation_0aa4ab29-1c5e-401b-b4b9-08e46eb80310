import os

from utils.typings import AttacksType, FreeRiderAttack
from aggregators.Aggregator import allAggregators
from typing import List
import torch
from experiment.DefaultExperimentConfiguration import DefaultExperimentConfiguration
# Naked imports for allAggregators function
#from aggregators.ModAFA import Mod<PERSON>AAggregator
#from aggregators.AFA import AFAAggregator
#from aggregators.FedMGDAplus import FedMGDAplusAggregator
#from aggregators.FedMGDAplusplus import FedMGDAplusplusAggregator
from aggregators.FedAvg import FedAvgAggregator
#from aggregators.RFCL import RFCLAggregator
#from aggregators.Clustering import ClusteringAggregator

from aggregators.FedRAD import FedRADAggregator
from aggregators.FedBE import FedBEAggregator
from aggregators.FedDF import FedDFAggregator



#from aggregators.CC import CenteredClippingAggregator
#from aggregators.RFA import RFAAggregator
#from aggregators.MKrum import MKrumAggregator
#from aggregators.RFCL_With_FedAvg_Internal_Aggregator import RFCL_With_FedAvg_Internal_AggAggregator
#from aggregators.RFCL_Without_PCA import RFCL_Without_PCAAggregator
#from aggregators.KMeans import KMeansAggregator
#from aggregators.HDBSCAN import HDBSCANAggregator
#from aggregators.Agglomerative import AgglomerativeAggregator
from aggregators.Foolsgold import FoolsgoldAggregator
from aggregators.FLAME import FLAMEAggregator
from aggregators.RLR import RLRAggregator

#from aggregators.FedRAD import FedRADAggregator 

from aggregators.RKD import RKDAggregator
#from aggregators.RUFL import RUFLAggregator

from aggregators.without_KD import without_KDAggregator


class CustomConfig(DefaultExperimentConfiguration):
    def __init__(self):
        super().__init__()

        self.nonIID = True
        self.alphaDirichlet = 0.3  # For sampling
        self.serverDataSize = 1.0 / 6
        # self.aggregatorConfig.rounds = 10

        if self.nonIID:
            iidString = f"non-IID alpha={self.alphaDirichlet}"
        else:
            iidString = "IID"
        
        # Use differential privacy or not. Note: This doesn't work. Only the release proportion is currently turned on
        self.privacyPreserve = False  # if None, run with AND without DP
        # self.releaseProportion: float = 0.5
        # self.epsilon1: float = 0.01
        # self.epsilon3: float = 0.01
        # self.needClip: bool = False
        # self.clipValue: float = 0.0001
        # self.needNormalization: bool = False

        # Privacy Amplification settings  (Sets how many clients are sampled)
        self.privacyAmplification = False
        # self.amplificationP = 0.33

        # self.aggregatorConfig.rounds = 30
        # self.epochs = 10
        # self.momentum = 0.8
        # self.lr = 0.00001
        # self.batchSize = 32

        self.scenarios: AttacksType = [
            # No attack baseline
            ([], [], [], f"No Attacks {iidString}"),

            # A3FL (A Little Is Enough) attacks with different percentages
            ([], [], [2, 5, 8], f"20% A3FL Attack {iidString}"),
            ([], [], [2, 5, 8, 11, 14, 17], f"40% A3FL Attack {iidString}"),
            ([], [], [2, 5, 8, 11, 14, 17, 20, 23, 26],
             f"60% A3FL Attack {iidString}"),
        ]

        self.percUsers = torch.tensor(PERC_USERS,
                                      device=self.aggregatorConfig.device)

        # Baseline and RKD aggregators for comparison
        self.aggregators = [
            FedAvgAggregator,
            RKDAggregator,
            FLAMEAggregator,
            FedDFAggregator,
            FedRADAggregator,
            FedBEAggregator,
            RLRAggregator,
            FoolsgoldAggregator,
        ]

    def scenario_conversion(self):
        """
        Sets the faulty, malicious and free-riding clients appropriately.

        Sets the config's and aggregatorConfig's names to be the attackName.
        """
        for faulty, malicious, freeRider, attackName in self.scenarios:

            self.faulty = faulty
            self.malicious = malicious
            self.freeRiding = freeRider
            self.name = attackName
            self.aggregatorConfig.attackName = attackName

            yield attackName


# Determines how much data each client gets (normalised)
PERC_USERS: List[float] = [
    0.2,
    0.15,
    0.2,
    0.2,
    0.1,
    0.15,
    0.1,
    0.15,
    0.2,
    0.2,
    0.2,
    0.3,
    0.2,
    0.2,
    0.1,
    #0.1,
    #0.1,
    #0.15,
    #0.2,
    #0.2,
    #0.1,
    #0.15,
    #0.2,
    #0.2,
    #0.1,
    #0.15,
    #0.1,
    #0.15,
    #0.2,
    #0.2,
]
